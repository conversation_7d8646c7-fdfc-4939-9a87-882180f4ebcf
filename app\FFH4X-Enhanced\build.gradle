apply plugin: 'com.android.application'
apply plugin: 'com.google.gms.google-services'

android {
    compileSdkVersion 34
    buildToolsVersion "34.0.0"

    defaultConfig {
        applicationId "com.my.ffh4xinjector"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 2
        versionName "8.0"
        
        multiDexEnabled true
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        
        // Vector drawables support
        vectorDrawables.useSupportLibrary = true
        
        // ProGuard configuration
        proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            
            // Signing config for release
            signingConfig signingConfigs.release
        }
        
        debug {
            minifyEnabled false
            debuggable true
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
        }
    }

    signingConfigs {
        release {
            // Add your signing configuration here
            // storeFile file('path/to/your/keystore.jks')
            // storePassword 'your_store_password'
            // keyAlias 'your_key_alias'
            // keyPassword 'your_key_password'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/ASL2.0'
        exclude 'META-INF/*.kotlin_module'
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    
    // AndroidX Libraries
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.core:core:1.12.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'androidx.viewpager2:viewpager2:1.0.0'
    implementation 'androidx.fragment:fragment:1.6.2'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation 'androidx.work:work-runtime:2.9.0'
    
    // Material Design
    implementation 'com.google.android.material:material:1.11.0'
    
    // Firebase
    implementation platform('com.google.firebase:firebase-bom:32.7.0')
    implementation 'com.google.firebase:firebase-auth'
    implementation 'com.google.firebase:firebase-database'
    implementation 'com.google.firebase:firebase-messaging'
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-crashlytics'
    
    // Shizuku Integration
    implementation 'dev.rikka.shizuku:api:13.1.5'
    implementation 'dev.rikka.shizuku:provider:13.1.5'
    
    // Network & HTTP
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    
    // JSON Processing
    implementation 'com.google.code.gson:gson:2.10.1'
    
    // Image Loading
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.16.0'
    
    // Permissions
    implementation 'com.karumi:dexter:6.2.3'
    
    // Root Detection & Security
    implementation 'com.scottyab:rootbeer-lib:0.1.0'
    
    // Encryption
    implementation 'androidx.security:security-crypto:1.1.0-alpha06'
    
    // Logging
    implementation 'com.jakewharton.timber:timber:5.0.1'
    
    // Event Bus
    implementation 'org.greenrobot:eventbus:3.3.1'
    
    // Reactive Programming
    implementation 'io.reactivex.rxjava3:rxjava:3.1.8'
    implementation 'io.reactivex.rxjava3:rxandroid:3.0.2'
    
    // Dependency Injection
    implementation 'com.google.dagger:dagger:2.48.1'
    annotationProcessor 'com.google.dagger:dagger-compiler:2.48.1'
    
    // Testing
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:5.7.0'
    testImplementation 'org.robolectric:robolectric:4.11.1'
    
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation 'androidx.test:runner:1.5.2'
    androidTestImplementation 'androidx.test:rules:1.5.0'
    
    // Memory Leak Detection (Debug only)
    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.12'
    
    // Development Tools
    debugImplementation 'com.facebook.flipper:flipper:0.212.0'
    debugImplementation 'com.facebook.soloader:soloader:0.10.5'
    debugImplementation 'com.facebook.flipper:flipper-network-plugin:0.212.0'
    
    // MultiDex Support
    implementation 'androidx.multidex:multidex:2.0.1'
}

// Apply Google Services plugin
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
