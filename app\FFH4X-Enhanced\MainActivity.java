package com.my.ffh4xinjector;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.provider.Settings;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;

import com.my.ffh4xinjector.license.LifetimeUserManager;
import com.my.ffh4xinjector.ui.LifetimeAccessController;
import com.my.ffh4xinjector.ui.AutoAimPanelController;
import com.my.ffh4xinjector.ui.WiFiDebugController;
import com.my.ffh4xinjector.system.ShizukuIntegrationManager;
import com.my.ffh4xinjector.compatibility.UniversalCompatibilityManager;

/**
 * Enhanced Main Activity for FFH4X v8
 * Integrates lifetime access, universal compatibility, and Shizuku WiFi debugging
 */
public class MainActivity extends AppCompatActivity {
    private static final String TAG = "MainActivity";
    private static final int REQUEST_OVERLAY_PERMISSION = 1001;
    
    // Managers
    private LifetimeUserManager lifetimeUserManager;
    private UniversalCompatibilityManager compatibilityManager;
    private ShizukuIntegrationManager shizukuManager;
    
    // Controllers
    private LifetimeAccessController lifetimeAccessController;
    private AutoAimPanelController autoAimController;
    private WiFiDebugController wifiDebugController;
    
    // UI Components
    private LinearLayout mainContainer;
    private CardView lifetimeAccessCard;
    private CardView autoAimCard;
    private CardView wifiDebugCard;
    private Button overlayPermissionButton;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        initializeManagers();
        initializeUI();
        checkPermissions();
        setupControllers();
        checkLifetimeAccess();
    }
    
    /**
     * Initialize all managers
     */
    private void initializeManagers() {
        lifetimeUserManager = LifetimeUserManager.getInstance(this);
        compatibilityManager = UniversalCompatibilityManager.getInstance(this);
        shizukuManager = ShizukuIntegrationManager.getInstance(this);
        
        // Log device compatibility info
        UniversalCompatibilityManager.DeviceInfo deviceInfo = compatibilityManager.getDeviceInfo();
        UniversalCompatibilityManager.CompatibilityProfile profile = compatibilityManager.getCompatibilityProfile();
        
        Toast.makeText(this, "Device: " + deviceInfo.toString() + 
                      "\nCompatibility: " + profile.overallCompatibility, 
                      Toast.LENGTH_LONG).show();
    }
    
    /**
     * Initialize UI components
     */
    private void initializeUI() {
        mainContainer = findViewById(R.id.main_container);
        lifetimeAccessCard = findViewById(R.id.card_lifetime_access);
        autoAimCard = findViewById(R.id.card_autoaim);
        wifiDebugCard = findViewById(R.id.card_wifi_debug);
        overlayPermissionButton = findViewById(R.id.btn_overlay_permission);
        
        // Set up overlay permission button
        overlayPermissionButton.setOnClickListener(v -> requestOverlayPermission());
    }
    
    /**
     * Check required permissions
     */
    private void checkPermissions() {
        // Check overlay permission
        if (!Settings.canDrawOverlays(this)) {
            overlayPermissionButton.setVisibility(View.VISIBLE);
            overlayPermissionButton.setText("Grant Overlay Permission");
        } else {
            overlayPermissionButton.setVisibility(View.GONE);
        }
        
        // Check battery optimization
        checkBatteryOptimization();
    }
    
    /**
     * Setup all controllers
     */
    private void setupControllers() {
        // Lifetime Access Controller
        lifetimeAccessController = new LifetimeAccessController(this);
        lifetimeAccessController.setOnAccessChangeListener(new LifetimeAccessController.OnAccessChangeListener() {
            @Override
            public void onAccessGranted(int accessType) {
                enablePremiumFeatures();
                updateUIForLifetimeAccess(true);
            }
            
            @Override
            public void onAccessDenied() {
                disablePremiumFeatures();
                updateUIForLifetimeAccess(false);
            }
            
            @Override
            public void onActivationRequired() {
                showActivationDialog();
            }
        });
        
        // AutoAim Controller
        autoAimController = new AutoAimPanelController(this);
        autoAimController.setOnSettingsChangeListener(new AutoAimPanelController.OnSettingsChangeListener() {
            @Override
            public void onSettingsChanged() {
                // Handle autoaim settings change
                Toast.makeText(MainActivity.this, "AutoAim settings updated", Toast.LENGTH_SHORT).show();
            }
            
            @Override
            public void onAutoAimToggled(boolean enabled) {
                // Handle autoaim toggle
                String status = enabled ? "enabled" : "disabled";
                Toast.makeText(MainActivity.this, "AutoAim " + status, Toast.LENGTH_SHORT).show();
            }
        });
        
        // WiFi Debug Controller
        wifiDebugController = new WiFiDebugController(this);
        wifiDebugController.setOnWifiDebugChangeListener(new WiFiDebugController.OnWifiDebugChangeListener() {
            @Override
            public void onWifiDebugEnabled(int port) {
                Toast.makeText(MainActivity.this, 
                    "WiFi debugging enabled on port " + port, Toast.LENGTH_LONG).show();
            }
            
            @Override
            public void onWifiDebugDisabled() {
                Toast.makeText(MainActivity.this, 
                    "WiFi debugging disabled", Toast.LENGTH_SHORT).show();
            }
            
            @Override
            public void onShizukuStatusChanged(boolean available) {
                updateShizukuStatus(available);
            }
        });
        
        // Add controller views to cards
        addControllerViews();
    }
    
    /**
     * Add controller views to their respective cards
     */
    private void addControllerViews() {
        // Add lifetime access panel
        LinearLayout lifetimeContainer = lifetimeAccessCard.findViewById(R.id.lifetime_container);
        if (lifetimeContainer != null) {
            lifetimeContainer.addView(lifetimeAccessController.getPanelView());
        }
        
        // Add autoaim panel
        LinearLayout autoaimContainer = autoAimCard.findViewById(R.id.autoaim_container);
        if (autoaimContainer != null) {
            autoaimContainer.addView(autoAimController.getPanelView());
        }
        
        // Add wifi debug panel
        LinearLayout wifiContainer = wifiDebugCard.findViewById(R.id.wifi_container);
        if (wifiContainer != null) {
            wifiContainer.addView(wifiDebugController.getPanelView());
        }
    }
    
    /**
     * Check initial lifetime access status
     */
    private void checkLifetimeAccess() {
        lifetimeUserManager.checkAccessStatus(new LifetimeUserManager.OnAccessCheckListener() {
            @Override
            public void onAccessResult(int accessStatus, String message) {
                runOnUiThread(() -> {
                    if (accessStatus == LifetimeUserManager.ACCESS_LIFETIME) {
                        enablePremiumFeatures();
                        updateUIForLifetimeAccess(true);
                        Toast.makeText(MainActivity.this, "Welcome back! " + message, Toast.LENGTH_SHORT).show();
                    } else {
                        disablePremiumFeatures();
                        updateUIForLifetimeAccess(false);
                        if (accessStatus == LifetimeUserManager.ACCESS_DENIED) {
                            showActivationDialog();
                        }
                    }
                });
            }
        });
    }
    
    /**
     * Enable premium features
     */
    private void enablePremiumFeatures() {
        // Enable AutoAim
        autoAimController.setAutoAimEnabled(true);
        
        // Enable advanced features based on device compatibility
        UniversalCompatibilityManager.CompatibilityProfile profile = 
            compatibilityManager.getCompatibilityProfile();
        
        if (profile.overlayCompatibility != UniversalCompatibilityManager.CompatibilityLevel.UNSUPPORTED) {
            // Enable overlay features
            enableOverlayFeatures();
        }
        
        if (profile.shizukuCompatibility != UniversalCompatibilityManager.CompatibilityLevel.UNSUPPORTED) {
            // Enable Shizuku features
            enableShizukuFeatures();
        }
    }
    
    /**
     * Disable premium features
     */
    private void disablePremiumFeatures() {
        autoAimController.setAutoAimEnabled(false);
        disableOverlayFeatures();
        disableShizukuFeatures();
    }
    
    /**
     * Update UI based on lifetime access status
     */
    private void updateUIForLifetimeAccess(boolean hasAccess) {
        // Update card visibility and styling
        autoAimCard.setAlpha(hasAccess ? 1.0f : 0.5f);
        wifiDebugCard.setAlpha(hasAccess ? 1.0f : 0.5f);
        
        // Update card backgrounds
        int cardColor = hasAccess ? R.color.card_background_premium : R.color.card_background;
        autoAimCard.setCardBackgroundColor(getColor(cardColor));
        wifiDebugCard.setCardBackgroundColor(getColor(cardColor));
    }
    
    /**
     * Show activation dialog
     */
    private void showActivationDialog() {
        Intent intent = new Intent(this, LicenseActivationActivity.class);
        startActivity(intent);
    }
    
    /**
     * Enable overlay features
     */
    private void enableOverlayFeatures() {
        if (Settings.canDrawOverlays(this)) {
            // Start overlay service
            Intent overlayIntent = new Intent(this, OverlayService.class);
            startForegroundService(overlayIntent);
        }
    }
    
    /**
     * Disable overlay features
     */
    private void disableOverlayFeatures() {
        Intent overlayIntent = new Intent(this, OverlayService.class);
        stopService(overlayIntent);
    }
    
    /**
     * Enable Shizuku features
     */
    private void enableShizukuFeatures() {
        if (shizukuManager.isShizukuAvailable() && shizukuManager.hasShizukuPermission()) {
            // Shizuku features are ready
            wifiDebugController.refreshPanel();
        }
    }
    
    /**
     * Disable Shizuku features
     */
    private void disableShizukuFeatures() {
        // Disable WiFi debugging if active
        if (wifiDebugController.isWifiDebuggingEnabled()) {
            // WiFi debugging will be disabled by the controller
        }
    }
    
    /**
     * Update Shizuku status
     */
    private void updateShizukuStatus(boolean available) {
        // Update UI based on Shizuku availability
        wifiDebugCard.setAlpha(available ? 1.0f : 0.7f);
    }
    
    /**
     * Request overlay permission
     */
    private void requestOverlayPermission() {
        Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION);
        intent.setData(Uri.parse("package:" + getPackageName()));
        startActivityForResult(intent, REQUEST_OVERLAY_PERMISSION);
    }
    
    /**
     * Check battery optimization
     */
    private void checkBatteryOptimization() {
        // Implementation for battery optimization check
        // This helps with background services and overlay performance
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (requestCode == REQUEST_OVERLAY_PERMISSION) {
            if (Settings.canDrawOverlays(this)) {
                overlayPermissionButton.setVisibility(View.GONE);
                Toast.makeText(this, "Overlay permission granted", Toast.LENGTH_SHORT).show();
                
                // Re-check lifetime access to enable overlay features if user has access
                if (lifetimeUserManager.hasLifetimeAccess()) {
                    enableOverlayFeatures();
                }
            } else {
                Toast.makeText(this, "Overlay permission required for full functionality", Toast.LENGTH_LONG).show();
            }
        }
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        
        // Refresh all controllers
        lifetimeAccessController.refreshPanel();
        autoAimController.refreshPanel();
        wifiDebugController.refreshPanel();
        
        // Check permissions again
        checkPermissions();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        // Cleanup
        if (wifiDebugController != null) {
            wifiDebugController.cleanup();
        }
    }
}
