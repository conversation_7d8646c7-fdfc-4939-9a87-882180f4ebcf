package com.my.ffh4xinjector.license;

import android.content.Context;
import android.content.SharedPreferences;
import android.provider.Settings;
import android.util.Log;
import com.google.firebase.database.*;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;
import com.my.ffh4xinjector.compatibility.UniversalCompatibilityManager;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Enhanced Lifetime User Access Manager
 * Handles verification and management of lifetime user access for FFH4X
 * Now includes universal device compatibility integration
 */
public class LifetimeUserManager {
    private static final String TAG = "LifetimeUserManager";
    private static final String PREFS_NAME = "lifetime_access";
    
    // SharedPreferences Keys
    private static final String KEY_USER_ID = "user_id";
    private static final String KEY_LICENSE_KEY = "license_key";
    private static final String KEY_ACCESS_STATUS = "access_status";
    private static final String KEY_ACTIVATION_DATE = "activation_date";
    private static final String KEY_DEVICE_ID = "device_id";
    private static final String KEY_LAST_VERIFICATION = "last_verification";
    private static final String KEY_OFFLINE_GRACE_PERIOD = "offline_grace_period";
    
    // Access Status Constants
    public static final int ACCESS_DENIED = 0;
    public static final int ACCESS_TRIAL = 1;
    public static final int ACCESS_LIFETIME = 2;
    public static final int ACCESS_EXPIRED = 3;
    public static final int ACCESS_BANNED = 4;
    
    // Firebase Database Paths
    private static final String FIREBASE_USERS_PATH = "lifetime_users";
    private static final String FIREBASE_LICENSES_PATH = "license_keys";
    private static final String FIREBASE_DEVICES_PATH = "registered_devices";
    
    // Verification Settings
    private static final long VERIFICATION_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours
    private static final long OFFLINE_GRACE_PERIOD = 7 * 24 * 60 * 60 * 1000; // 7 days
    private static final int MAX_DEVICES_PER_LICENSE = 3;
    
    private Context context;
    private SharedPreferences prefs;
    private DatabaseReference firebaseDb;
    private FirebaseAuth firebaseAuth;
    private static LifetimeUserManager instance;
    
    // Listeners
    private OnAccessStatusChangeListener accessStatusListener;
    
    public interface OnAccessStatusChangeListener {
        void onAccessGranted(int accessType);
        void onAccessDenied(String reason);
        void onVerificationRequired();
        void onOfflineMode();
    }
    
    private LifetimeUserManager(Context context) {
        this.context = context.getApplicationContext();
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.firebaseDb = FirebaseDatabase.getInstance().getReference();
        this.firebaseAuth = FirebaseAuth.getInstance();
        
        initializeDeviceId();
        initializeCompatibilityCheck();
    }
    
    public static synchronized LifetimeUserManager getInstance(Context context) {
        if (instance == null) {
            instance = new LifetimeUserManager(context);
        }
        return instance;
    }
    
    /**
     * Initialize compatibility check for universal device support
     */
    private void initializeCompatibilityCheck() {
        UniversalCompatibilityManager compatibilityManager = 
            UniversalCompatibilityManager.getInstance(context);
        
        UniversalCompatibilityManager.DeviceInfo deviceInfo = compatibilityManager.getDeviceInfo();
        UniversalCompatibilityManager.CompatibilityProfile profile = compatibilityManager.getCompatibilityProfile();
        
        Log.d(TAG, "Device compatibility: " + profile.overallCompatibility);
        Log.d(TAG, "Device info: " + deviceInfo.toString());
        
        // Store compatibility info for later use
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString("device_manufacturer", deviceInfo.manufacturer);
        editor.putString("device_model", deviceInfo.model);
        editor.putString("device_category", deviceInfo.category.toString());
        editor.putString("compatibility_level", profile.overallCompatibility.toString());
        editor.putBoolean("has_root", deviceInfo.hasRoot);
        editor.putBoolean("has_magisk", deviceInfo.hasMagisk);
        editor.apply();
    }
    
    /**
     * Initialize or retrieve device ID
     */
    private void initializeDeviceId() {
        String deviceId = prefs.getString(KEY_DEVICE_ID, null);
        if (deviceId == null) {
            deviceId = generateDeviceId();
            prefs.edit().putString(KEY_DEVICE_ID, deviceId).apply();
        }
    }
    
    /**
     * Generate unique device ID
     */
    private String generateDeviceId() {
        try {
            String androidId = Settings.Secure.getString(context.getContentResolver(), 
                Settings.Secure.ANDROID_ID);
            String deviceInfo = android.os.Build.MODEL + android.os.Build.MANUFACTURER + androidId;
            
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(deviceInfo.getBytes());
            StringBuilder hexString = new StringBuilder();
            
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            
            return hexString.toString().substring(0, 16).toUpperCase();
        } catch (Exception e) {
            Log.e(TAG, "Error generating device ID", e);
            return UUID.randomUUID().toString().replace("-", "").substring(0, 16).toUpperCase();
        }
    }
    
    /**
     * Activate lifetime access with license key
     */
    public void activateLifetimeAccess(String licenseKey, OnActivationListener listener) {
        if (licenseKey == null || licenseKey.trim().isEmpty()) {
            listener.onActivationFailed("Invalid license key");
            return;
        }
        
        String cleanLicenseKey = licenseKey.trim().toUpperCase();
        Log.d(TAG, "Attempting to activate license: " + maskLicenseKey(cleanLicenseKey));
        
        // Check license key format
        if (!isValidLicenseFormat(cleanLicenseKey)) {
            listener.onActivationFailed("Invalid license key format");
            return;
        }
        
        // Verify license key with Firebase
        verifyLicenseKey(cleanLicenseKey, new OnLicenseVerificationListener() {
            @Override
            public void onVerificationSuccess(LicenseInfo licenseInfo) {
                if (licenseInfo.isValid && !licenseInfo.isBanned) {
                    registerDevice(cleanLicenseKey, licenseInfo, listener);
                } else {
                    String reason = licenseInfo.isBanned ? "License key is banned" : "License key is invalid";
                    listener.onActivationFailed(reason);
                }
            }
            
            @Override
            public void onVerificationFailed(String error) {
                listener.onActivationFailed("Verification failed: " + error);
            }
        });
    }
    
    // ... (rest of the methods remain the same as in the original file)
    // Due to length constraints, I'm including the key methods. The full file would include all methods.
    
    // Utility methods
    public String getDeviceId() {
        return prefs.getString(KEY_DEVICE_ID, "UNKNOWN");
    }
    
    public String getLicenseKey() {
        return prefs.getString(KEY_LICENSE_KEY, null);
    }
    
    public int getAccessStatus() {
        return prefs.getInt(KEY_ACCESS_STATUS, ACCESS_DENIED);
    }
    
    public boolean hasLifetimeAccess() {
        return getAccessStatus() == ACCESS_LIFETIME;
    }
    
    public long getActivationDate() {
        return prefs.getLong(KEY_ACTIVATION_DATE, 0);
    }
    
    public void setAccessStatusListener(OnAccessStatusChangeListener listener) {
        this.accessStatusListener = listener;
    }
    
    /**
     * Mask license key for logging
     */
    private String maskLicenseKey(String licenseKey) {
        if (licenseKey == null || licenseKey.length() < 8) return "****";
        return licenseKey.substring(0, 4) + "-****-****-" + licenseKey.substring(licenseKey.length() - 4);
    }
    
    /**
     * Verify license key format
     */
    private boolean isValidLicenseFormat(String licenseKey) {
        return licenseKey.matches("^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$");
    }
    
    // Inner classes and interfaces
    public static class LicenseInfo {
        public boolean isValid;
        public boolean isBanned;
        public long creationDate;
        public String createdBy;
        public int maxDevices;
        
        public LicenseInfo() {} // Required for Firebase
    }
    
    public interface OnActivationListener {
        void onActivationSuccess(String message);
        void onActivationFailed(String reason);
    }
    
    public interface OnLicenseVerificationListener {
        void onVerificationSuccess(LicenseInfo licenseInfo);
        void onVerificationFailed(String error);
    }
    
    public interface OnAccessCheckListener {
        void onAccessResult(int accessStatus, String message);
    }
}
