# FFH4X Enhanced v8.0 - Compilation Guide

## 🚀 **Quick Start Guide**

### **Step 1: Prerequisites**
```bash
# Required Software:
- Android Studio Hedgehog (2023.1.1) or later
- Android SDK API 21-34
- Java Development Kit (JDK) 8 or 11
- Git (for version control)
- Firebase CLI (optional)
```

### **Step 2: Project Setup**
```bash
# 1. Extract/Clone the project
cd FFH4X-Enhanced/

# 2. Open in Android Studio
# File → Open → Select FFH4X-Enhanced folder

# 3. Wait for Gradle sync to complete
# This may take 5-10 minutes on first run
```

### **Step 3: Firebase Configuration**
```bash
# 1. Create Firebase project at https://console.firebase.google.com
# 2. Add Android app with package: com.my.ffh4xinjector
# 3. Download google-services.json
# 4. Place in app/ directory (same level as build.gradle)
# 5. Enable Authentication, Realtime Database, and Crashlytics
```

### **Step 4: Build Configuration**
```gradle
// In app/build.gradle, update signing config:
signingConfigs {
    release {
        storeFile file('path/to/your/keystore.jks')
        storePassword 'your_store_password'
        keyAlias 'your_key_alias'
        keyPassword 'your_key_password'
    }
}
```

### **Step 5: Build APK**
```bash
# Debug Build (for testing)
./gradlew assembleDebug

# Release Build (for distribution)
./gradlew assembleRelease

# APK Location:
# app/build/outputs/apk/debug/app-debug.apk
# app/build/outputs/apk/release/app-release.apk
```

## 🔧 **Detailed Configuration**

### **Android Studio Setup**
1. **Install Android Studio**
   - Download from: https://developer.android.com/studio
   - Install with default settings
   - Install Android SDK API 21-34

2. **Configure SDK**
   ```
   Tools → SDK Manager → SDK Platforms
   ✅ Android 5.0 (API 21) - Minimum
   ✅ Android 14 (API 34) - Target
   ✅ Android SDK Build-Tools 34.0.0
   ✅ Android Emulator (for testing)
   ```

3. **Import Project**
   ```
   File → Open → Select FFH4X-Enhanced folder
   Wait for Gradle sync to complete
   ```

### **Firebase Setup (Required)**
1. **Create Firebase Project**
   ```
   1. Go to https://console.firebase.google.com
   2. Click "Create a project"
   3. Enter project name: "FFH4X-Enhanced"
   4. Enable Google Analytics (optional)
   ```

2. **Add Android App**
   ```
   1. Click "Add app" → Android
   2. Package name: com.my.ffh4xinjector
   3. App nickname: FFH4X Enhanced
   4. Download google-services.json
   5. Place in app/ directory
   ```

3. **Enable Services**
   ```
   Authentication:
   - Enable Email/Password provider
   - Enable Anonymous authentication
   
   Realtime Database:
   - Create database in test mode
   - Set up security rules
   
   Crashlytics:
   - Enable crash reporting
   ```

### **Dependencies Configuration**
All dependencies are pre-configured in `build.gradle`:

```gradle
// Core Android Libraries
implementation 'androidx.appcompat:appcompat:1.6.1'
implementation 'com.google.android.material:material:1.11.0'

// Firebase
implementation platform('com.google.firebase:firebase-bom:32.7.0')
implementation 'com.google.firebase:firebase-auth'
implementation 'com.google.firebase:firebase-database'

// Shizuku Integration
implementation 'dev.rikka.shizuku:api:13.1.5'
implementation 'dev.rikka.shizuku:provider:13.1.5'

// Network & Security
implementation 'com.squareup.okhttp3:okhttp:4.12.0'
implementation 'androidx.security:security-crypto:1.1.0-alpha06'
```

## 🔐 **Signing Configuration**

### **Generate Keystore**
```bash
# Create new keystore
keytool -genkey -v -keystore ffh4x-release.jks -keyalg RSA -keysize 2048 -validity 10000 -alias ffh4x

# Enter details when prompted:
# - Store password: [secure password]
# - Key password: [secure password]
# - Name: FFH4X Enhanced
# - Organization: Your Organization
```

### **Configure Signing**
```gradle
// In app/build.gradle
android {
    signingConfigs {
        release {
            storeFile file('ffh4x-release.jks')
            storePassword 'your_store_password'
            keyAlias 'ffh4x'
            keyPassword 'your_key_password'
        }
    }
    
    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}
```

## 🏗️ **Build Variants**

### **Debug Build**
```bash
# Build debug APK
./gradlew assembleDebug

# Features:
- Debugging enabled
- No obfuscation
- Faster build time
- Package: com.my.ffh4xinjector.debug
```

### **Release Build**
```bash
# Build release APK
./gradlew assembleRelease

# Features:
- Code obfuscation (ProGuard)
- Resource shrinking
- Optimized performance
- Signed with release key
```

### **Build Optimization**
```gradle
// In gradle.properties
org.gradle.jvmargs=-Xmx4096m
org.gradle.parallel=true
org.gradle.caching=true
android.enableR8.fullMode=true
```

## 📱 **Testing Configuration**

### **Device Testing**
```bash
# Test on different devices:
1. Budget devices (Android 5.0-7.0)
2. Mid-range devices (Android 8.0-11.0)
3. Flagship devices (Android 12.0+)
4. Gaming phones (ROG, RedMagic, etc.)
5. Chinese OEMs (Xiaomi, Huawei, OPPO)
```

### **Emulator Setup**
```bash
# Create test emulators:
1. Pixel 3a (API 28) - Mid-range simulation
2. Pixel 6 (API 33) - Modern device
3. Galaxy S21 (API 31) - Samsung testing
4. Custom device (API 21) - Minimum support
```

### **Feature Testing**
```bash
# Test checklist:
✅ License activation and verification
✅ AutoAim functionality
✅ Shizuku integration and WiFi debugging
✅ Overlay permissions and visual feedback
✅ Device compatibility detection
✅ Firebase connectivity
✅ Offline mode and grace period
```

## 🚨 **Troubleshooting**

### **Common Build Issues**

1. **Gradle Sync Failed**
   ```bash
   # Solution:
   File → Invalidate Caches and Restart
   ./gradlew clean
   ./gradlew build
   ```

2. **Firebase Configuration Error**
   ```bash
   # Check:
   - google-services.json in correct location
   - Package name matches Firebase project
   - Firebase services enabled
   ```

3. **Shizuku API Issues**
   ```bash
   # Verify:
   - Shizuku dependency version
   - Manifest permissions
   - API compatibility
   ```

4. **ProGuard Errors**
   ```bash
   # Check proguard-rules.pro:
   - Keep rules for Firebase
   - Keep rules for Shizuku
   - Keep model classes
   ```

### **Performance Issues**
```bash
# Optimization tips:
1. Enable R8 full mode
2. Use build cache
3. Increase heap size
4. Enable parallel builds
5. Use latest Gradle version
```

## 📦 **Distribution**

### **APK Optimization**
```bash
# Before distribution:
1. Test on multiple devices
2. Verify license system
3. Check Firebase connectivity
4. Test offline functionality
5. Validate security features
```

### **Release Checklist**
```bash
✅ Version code incremented
✅ Version name updated
✅ Signed with release key
✅ ProGuard enabled
✅ Resources shrunk
✅ Tested on target devices
✅ Firebase configured
✅ License system verified
✅ Documentation updated
```

## 🔄 **Continuous Integration**

### **GitHub Actions (Optional)**
```yaml
# .github/workflows/build.yml
name: Build APK
on: [push, pull_request]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-java@v3
      with:
        java-version: '11'
    - run: ./gradlew assembleDebug
```

## 📞 **Support**

### **Build Support**
- Check Android Studio logs
- Verify Firebase configuration
- Test on physical devices
- Review ProGuard rules
- Check dependency versions

### **Runtime Issues**
- Enable debug logging
- Check device compatibility
- Verify permissions
- Test network connectivity
- Review crash reports

This compilation guide ensures successful building of FFH4X Enhanced v8.0 with all features properly configured and tested.
