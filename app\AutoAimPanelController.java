package com.my.ffh4xinjector.ui;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.*;
import androidx.cardview.widget.CardView;
import com.my.ffh4xinjector.autoaim.AutoAimConfig;
import com.my.ffh4xinjector.R;

/**
 * Enhanced AutoAim Panel Controller
 * Manages the UI interactions and real-time updates for the autoaim panel
 */
public class AutoAimPanelController {
    private static final String TAG = "AutoAimPanelController";
    
    private Context context;
    private View panelView;
    private AutoAimConfig config;
    
    // UI Components
    private Switch masterSwitch;
    private SeekBar sensitivitySeekBar;
    private SeekBar rangeSeekBar;
    private Spinner targetPrioritySpinner;
    private Switch smoothingSwitch;
    private Switch headshotSwitch;
    private Switch visibleOnlySwitch;
    private Switch autoFireSwitch;
    private TextView sensitivityValue;
    private TextView rangeValue;
    private Button resetButton;
    private Button applyButton;
    
    // Listeners
    private OnSettingsChangeListener settingsChangeListener;
    
    public interface OnSettingsChangeListener {
        void onSettingsChanged();
        void onAutoAimToggled(boolean enabled);
    }
    
    public AutoAimPanelController(Context context) {
        this.context = context;
        this.config = AutoAimConfig.getInstance(context);
        initializePanel();
    }
    
    private void initializePanel() {
        LayoutInflater inflater = LayoutInflater.from(context);
        panelView = inflater.inflate(R.layout.improved_autoaim_panel, null);
        
        findViews();
        setupListeners();
        loadCurrentSettings();
        updateUI();
    }
    
    private void findViews() {
        masterSwitch = panelView.findViewById(R.id.switch_autoaim_master);
        sensitivitySeekBar = panelView.findViewById(R.id.seekbar_sensitivity);
        rangeSeekBar = panelView.findViewById(R.id.seekbar_range);
        targetPrioritySpinner = panelView.findViewById(R.id.spinner_target_priority);
        smoothingSwitch = panelView.findViewById(R.id.switch_smoothing);
        headshotSwitch = panelView.findViewById(R.id.switch_headshot);
        visibleOnlySwitch = panelView.findViewById(R.id.switch_visible_only);
        autoFireSwitch = panelView.findViewById(R.id.switch_auto_fire);
        sensitivityValue = panelView.findViewById(R.id.text_sensitivity_value);
        rangeValue = panelView.findViewById(R.id.text_range_value);
        resetButton = panelView.findViewById(R.id.btn_reset);
        applyButton = panelView.findViewById(R.id.btn_apply);
    }
    
    private void setupListeners() {
        // Master switch listener
        masterSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            config.setEnabled(isChecked);
            updatePanelState(isChecked);
            if (settingsChangeListener != null) {
                settingsChangeListener.onAutoAimToggled(isChecked);
            }
        });
        
        // Sensitivity seekbar
        sensitivitySeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    float sensitivity = progress / 100.0f * 2.0f; // 0-2.0 range
                    sensitivityValue.setText(String.format("%.1f", sensitivity));
                }
            }
            
            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}
            
            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                float sensitivity = seekBar.getProgress() / 100.0f * 2.0f;
                config.setSensitivity(sensitivity);
                notifySettingsChanged();
            }
        });
        
        // Range seekbar
        rangeSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    float range = progress; // Direct mapping to meters
                    rangeValue.setText(String.format("%.0fm", range));
                }
            }
            
            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}
            
            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                config.setRange(seekBar.getProgress());
                notifySettingsChanged();
            }
        });
        
        // Target priority spinner
        setupTargetPrioritySpinner();
        
        // Switch listeners
        smoothingSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            config.setSmoothingEnabled(isChecked);
            notifySettingsChanged();
        });
        
        headshotSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            config.setHeadshotPriority(isChecked);
            notifySettingsChanged();
        });
        
        visibleOnlySwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            config.setVisibleTargetsOnly(isChecked);
            notifySettingsChanged();
        });
        
        autoFireSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            config.setAutoFireEnabled(isChecked);
            notifySettingsChanged();
        });
        
        // Button listeners
        resetButton.setOnClickListener(v -> resetSettings());
        applyButton.setOnClickListener(v -> applySettings());
    }
    
    private void setupTargetPrioritySpinner() {
        String[] priorities = {"Closest Target", "Lowest Health", "Headshot Preference"};
        ArrayAdapter<String> adapter = new ArrayAdapter<>(context, 
            android.R.layout.simple_spinner_item, priorities);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        targetPrioritySpinner.setAdapter(adapter);
        
        targetPrioritySpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                config.setTargetPriority(position);
                notifySettingsChanged();
            }
            
            @Override
            public void onNothingSelected(AdapterView<?> parent) {}
        });
    }
    
    private void loadCurrentSettings() {
        masterSwitch.setChecked(config.isEnabled());
        
        // Convert sensitivity (0-2.0) to seekbar progress (0-100)
        int sensitivityProgress = (int) (config.getSensitivity() / 2.0f * 100);
        sensitivitySeekBar.setProgress(sensitivityProgress);
        sensitivityValue.setText(String.format("%.1f", config.getSensitivity()));
        
        rangeSeekBar.setProgress((int) config.getRange());
        rangeValue.setText(String.format("%.0fm", config.getRange()));
        
        targetPrioritySpinner.setSelection(config.getTargetPriority());
        smoothingSwitch.setChecked(config.isSmoothingEnabled());
        headshotSwitch.setChecked(config.isHeadshotPriority());
        visibleOnlySwitch.setChecked(config.isVisibleTargetsOnly());
        autoFireSwitch.setChecked(config.isAutoFireEnabled());
    }
    
    private void updateUI() {
        updatePanelState(config.isEnabled());
    }
    
    private void updatePanelState(boolean enabled) {
        // Enable/disable controls based on master switch
        sensitivitySeekBar.setEnabled(enabled);
        rangeSeekBar.setEnabled(enabled);
        targetPrioritySpinner.setEnabled(enabled);
        smoothingSwitch.setEnabled(enabled);
        headshotSwitch.setEnabled(enabled);
        visibleOnlySwitch.setEnabled(enabled);
        autoFireSwitch.setEnabled(enabled);
        
        // Update visual feedback
        float alpha = enabled ? 1.0f : 0.5f;
        sensitivitySeekBar.setAlpha(alpha);
        rangeSeekBar.setAlpha(alpha);
        targetPrioritySpinner.setAlpha(alpha);
    }
    
    private void resetSettings() {
        config.resetToDefaults();
        loadCurrentSettings();
        updateUI();
        notifySettingsChanged();
        
        Toast.makeText(context, "Settings reset to defaults", Toast.LENGTH_SHORT).show();
    }
    
    private void applySettings() {
        if (config.isValidConfiguration()) {
            Toast.makeText(context, "Settings applied successfully", Toast.LENGTH_SHORT).show();
            notifySettingsChanged();
        } else {
            Toast.makeText(context, "Invalid configuration detected", Toast.LENGTH_SHORT).show();
        }
    }
    
    private void notifySettingsChanged() {
        if (settingsChangeListener != null) {
            settingsChangeListener.onSettingsChanged();
        }
    }
    
    // Public methods
    public View getPanelView() {
        return panelView;
    }
    
    public void setOnSettingsChangeListener(OnSettingsChangeListener listener) {
        this.settingsChangeListener = listener;
    }
    
    public void refreshPanel() {
        loadCurrentSettings();
        updateUI();
    }
    
    public boolean isAutoAimEnabled() {
        return config.isEnabled();
    }
    
    public void setAutoAimEnabled(boolean enabled) {
        config.setEnabled(enabled);
        masterSwitch.setChecked(enabled);
        updatePanelState(enabled);
    }
}
