<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    android:fillViewport="true">

    <LinearLayout
        android:id="@+id/main_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingBottom="24dp">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_launcher"
                android:layout_marginEnd="16dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="FFH4X Enhanced"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Version 8.0 - Universal Edition"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary" />

            </LinearLayout>

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_settings"
                android:tint="@color/accent_color"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="4dp" />

        </LinearLayout>

        <!-- Overlay Permission Button -->
        <Button
            android:id="@+id/btn_overlay_permission"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="Grant Overlay Permission"
            android:textColor="@color/white"
            android:background="@drawable/button_warning"
            android:layout_marginBottom="16dp"
            android:visibility="gone" />

        <!-- Lifetime Access Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_lifetime_access"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="4dp">

                <!-- Card Header -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="16dp"
                    android:background="@drawable/card_header_background">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_vip_crown"
                        android:tint="@color/gold_accent" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Lifetime Access"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginStart="12dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="PREMIUM"
                        android:textSize="10sp"
                        android:textStyle="bold"
                        android:textColor="@color/gold_accent"
                        android:background="@drawable/premium_badge_background"
                        android:padding="6dp" />

                </LinearLayout>

                <!-- Lifetime Access Content Container -->
                <LinearLayout
                    android:id="@+id/lifetime_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- AutoAim Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_autoaim"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="4dp">

                <!-- Card Header -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="16dp"
                    android:background="@drawable/card_header_background">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_crosshair"
                        android:tint="@color/accent_color" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="AutoAim System"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginStart="12dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="ADVANCED"
                        android:textSize="10sp"
                        android:textStyle="bold"
                        android:textColor="@color/accent_color"
                        android:background="@drawable/feature_badge_background"
                        android:padding="6dp" />

                </LinearLayout>

                <!-- AutoAim Content Container -->
                <LinearLayout
                    android:id="@+id/autoaim_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- WiFi Debug Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_wifi_debug"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="4dp">

                <!-- Card Header -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="16dp"
                    android:background="@drawable/card_header_background">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_wifi_debug"
                        android:tint="@color/accent_color" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="WiFi Debugging"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginStart="12dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="SHIZUKU"
                        android:textSize="10sp"
                        android:textStyle="bold"
                        android:textColor="@color/accent_color"
                        android:background="@drawable/feature_badge_background"
                        android:padding="6dp" />

                </LinearLayout>

                <!-- WiFi Debug Content Container -->
                <LinearLayout
                    android:id="@+id/wifi_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Quick Actions Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Quick Actions"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <Button
                        android:id="@+id/btn_start_game"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:text="Start Game"
                        android:textColor="@color/white"
                        android:background="@drawable/button_success"
                        android:layout_marginEnd="8dp" />

                    <Button
                        android:id="@+id/btn_settings"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:text="Settings"
                        android:textColor="@color/accent_color"
                        android:background="@drawable/button_outline"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Footer -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="FFH4X Enhanced v8.0"
                android:textSize="12sp"
                android:textColor="@color/text_secondary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Universal Android Support • Shizuku Integration • Lifetime Access"
                android:textSize="10sp"
                android:textColor="@color/text_secondary"
                android:textAlignment="center"
                android:layout_marginTop="4dp" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
