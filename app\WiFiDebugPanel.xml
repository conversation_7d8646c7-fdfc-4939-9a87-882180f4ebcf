<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/panel_background"
    android:elevation="8dp">

    <!-- Header Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingBottom="16dp">

        <ImageView
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:src="@drawable/ic_wifi_debug"
            android:tint="@color/accent_color" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="WiFi Debugging"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginStart="12dp" />

        <TextView
            android:id="@+id/text_debug_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="DISABLED"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="@color/status_inactive"
            android:background="@drawable/status_badge_background"
            android:padding="6dp" />

    </LinearLayout>

    <!-- Shizuku Status Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/card_background">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Shizuku Status"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="12dp" />

            <!-- Shizuku Installation Status -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <ImageView
                    android:id="@+id/icon_shizuku_installed"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_check_circle"
                    android:tint="@color/status_inactive" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Shizuku Installed"
                    android:textSize="14sp"
                    android:textColor="@color/text_primary"
                    android:layout_marginStart="12dp" />

                <TextView
                    android:id="@+id/text_shizuku_installed"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="NO"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/status_inactive" />

            </LinearLayout>

            <!-- Shizuku Service Status -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <ImageView
                    android:id="@+id/icon_shizuku_running"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_check_circle"
                    android:tint="@color/status_inactive" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Service Running"
                    android:textSize="14sp"
                    android:textColor="@color/text_primary"
                    android:layout_marginStart="12dp" />

                <TextView
                    android:id="@+id/text_shizuku_running"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="NO"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/status_inactive" />

            </LinearLayout>

            <!-- Shizuku Permission Status -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <ImageView
                    android:id="@+id/icon_shizuku_permission"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_check_circle"
                    android:tint="@color/status_inactive" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Permission Granted"
                    android:textSize="14sp"
                    android:textColor="@color/text_primary"
                    android:layout_marginStart="12dp" />

                <TextView
                    android:id="@+id/text_shizuku_permission"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="NO"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/status_inactive" />

            </LinearLayout>

            <!-- Request Permission Button -->
            <Button
                android:id="@+id/btn_request_permission"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:text="Request Shizuku Permission"
                android:textColor="@color/white"
                android:background="@drawable/button_primary"
                android:visibility="gone" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- WiFi Debug Control Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/card_background">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="WiFi Debug Control"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="12dp" />

            <!-- Current Status -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Current Status:"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary" />

                <TextView
                    android:id="@+id/text_wifi_debug_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Disabled"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/status_inactive" />

            </LinearLayout>

            <!-- ADB Port -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="ADB Port:"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary" />

                <EditText
                    android:id="@+id/edit_adb_port"
                    android:layout_width="80dp"
                    android:layout_height="40dp"
                    android:text="5555"
                    android:textSize="14sp"
                    android:textAlignment="center"
                    android:inputType="number"
                    android:background="@drawable/edit_text_background"
                    android:textColor="@color/text_primary" />

            </LinearLayout>

            <!-- Control Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btn_enable_wifi_debug"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="Enable"
                    android:textColor="@color/white"
                    android:background="@drawable/button_success"
                    android:layout_marginEnd="8dp" />

                <Button
                    android:id="@+id/btn_disable_wifi_debug"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="Disable"
                    android:textColor="@color/white"
                    android:background="@drawable/button_danger"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Device Compatibility Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/card_background">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Device Compatibility"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="12dp" />

            <!-- Device Info -->
            <TextView
                android:id="@+id/text_device_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Loading device information..."
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:fontFamily="monospace"
                android:layout_marginBottom="12dp" />

            <!-- Compatibility Level -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Compatibility:"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary" />

                <TextView
                    android:id="@+id/text_compatibility_level"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="UNKNOWN"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/status_inactive"
                    android:background="@drawable/compatibility_badge_background"
                    android:padding="6dp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Instructions Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/info_background">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Setup Instructions"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="1. Install Shizuku app from GitHub or F-Droid\n2. Start Shizuku service (requires ADB or root)\n3. Grant permission to FFH4X\n4. Enable WiFi debugging with custom port\n5. Connect via ADB: adb connect [device_ip]:[port]"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:lineSpacingExtra="2dp" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</LinearLayout>
