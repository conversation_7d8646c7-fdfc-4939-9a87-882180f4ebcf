package com.my.ffh4xinjector.overlay;

import android.content.Context;
import android.graphics.*;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;
import com.my.ffh4xinjector.autoaim.AutoAimConfig;

/**
 * Enhanced Visual Feedback System for AutoAim
 * Provides real-time visual indicators for targeting and aim assistance
 */
public class AutoAimVisualFeedback extends View {
    private static final String TAG = "AutoAimVisualFeedback";
    
    // Visual Constants
    private static final int CROSSHAIR_SIZE = 40;
    private static final int TARGET_CIRCLE_RADIUS = 60;
    private static final int FOV_CIRCLE_ALPHA = 80;
    private static final float STROKE_WIDTH = 3.0f;
    
    // Colors
    private static final int COLOR_CROSSHAIR = Color.GREEN;
    private static final int COLOR_TARGET_LOCKED = Color.RED;
    private static final int COLOR_TARGET_POTENTIAL = Color.YELLOW;
    private static final int COLOR_FOV_CIRCLE = Color.CYAN;
    private static final int COLOR_RANGE_INDICATOR = Color.BLUE;
    
    private AutoAimConfig config;
    private Paint crosshairPaint;
    private Paint targetPaint;
    private Paint fovPaint;
    private Paint rangePaint;
    private Paint textPaint;
    
    // Target information
    private PointF currentTarget;
    private PointF[] potentialTargets;
    private boolean isTargetLocked;
    private float targetDistance;
    private String targetInfo;
    
    // Screen dimensions
    private int screenWidth;
    private int screenHeight;
    private PointF screenCenter;
    
    public AutoAimVisualFeedback(Context context) {
        super(context);
        this.config = AutoAimConfig.getInstance(context);
        initializePaints();
        setupScreenDimensions();
    }
    
    private void initializePaints() {
        // Crosshair paint
        crosshairPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        crosshairPaint.setColor(COLOR_CROSSHAIR);
        crosshairPaint.setStrokeWidth(STROKE_WIDTH);
        crosshairPaint.setStyle(Paint.Style.STROKE);
        
        // Target paint
        targetPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        targetPaint.setStrokeWidth(STROKE_WIDTH);
        targetPaint.setStyle(Paint.Style.STROKE);
        
        // FOV circle paint
        fovPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        fovPaint.setColor(COLOR_FOV_CIRCLE);
        fovPaint.setAlpha(FOV_CIRCLE_ALPHA);
        fovPaint.setStrokeWidth(2.0f);
        fovPaint.setStyle(Paint.Style.STROKE);
        
        // Range indicator paint
        rangePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        rangePaint.setColor(COLOR_RANGE_INDICATOR);
        rangePaint.setAlpha(100);
        rangePaint.setStrokeWidth(1.5f);
        rangePaint.setStyle(Paint.Style.STROKE);
        
        // Text paint
        textPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        textPaint.setColor(Color.WHITE);
        textPaint.setTextSize(24);
        textPaint.setTypeface(Typeface.DEFAULT_BOLD);
        textPaint.setShadowLayer(2, 1, 1, Color.BLACK);
    }
    
    private void setupScreenDimensions() {
        WindowManager wm = (WindowManager) getContext().getSystemService(Context.WINDOW_SERVICE);
        if (wm != null) {
            screenWidth = wm.getDefaultDisplay().getWidth();
            screenHeight = wm.getDefaultDisplay().getHeight();
            screenCenter = new PointF(screenWidth / 2f, screenHeight / 2f);
        }
    }
    
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        
        if (!config.isEnabled()) {
            return;
        }
        
        drawFOVCircle(canvas);
        drawRangeIndicator(canvas);
        drawCrosshair(canvas);
        drawTargets(canvas);
        drawTargetInfo(canvas);
        drawStatusIndicators(canvas);
    }
    
    private void drawCrosshair(Canvas canvas) {
        float centerX = screenCenter.x;
        float centerY = screenCenter.y;
        
        // Draw crosshair lines
        canvas.drawLine(centerX - CROSSHAIR_SIZE/2, centerY, 
                       centerX + CROSSHAIR_SIZE/2, centerY, crosshairPaint);
        canvas.drawLine(centerX, centerY - CROSSHAIR_SIZE/2, 
                       centerX, centerY + CROSSHAIR_SIZE/2, crosshairPaint);
        
        // Draw center dot
        canvas.drawCircle(centerX, centerY, 3, crosshairPaint);
    }
    
    private void drawFOVCircle(Canvas canvas) {
        if (config.getFovLimit() > 0) {
            float radius = (config.getFovLimit() / 180f) * Math.min(screenWidth, screenHeight) / 2;
            canvas.drawCircle(screenCenter.x, screenCenter.y, radius, fovPaint);
        }
    }
    
    private void drawRangeIndicator(Canvas canvas) {
        // Draw range circles at 25%, 50%, 75%, and 100% of max range
        float maxRange = config.getRange();
        float baseRadius = Math.min(screenWidth, screenHeight) / 4f;
        
        for (int i = 1; i <= 4; i++) {
            float radius = baseRadius * (i / 4f);
            rangePaint.setAlpha(100 - (i * 20)); // Fade out with distance
            canvas.drawCircle(screenCenter.x, screenCenter.y, radius, rangePaint);
        }
    }
    
    private void drawTargets(Canvas canvas) {
        // Draw current locked target
        if (currentTarget != null && isTargetLocked) {
            targetPaint.setColor(COLOR_TARGET_LOCKED);
            canvas.drawCircle(currentTarget.x, currentTarget.y, TARGET_CIRCLE_RADIUS, targetPaint);
            
            // Draw line from center to target
            canvas.drawLine(screenCenter.x, screenCenter.y, 
                           currentTarget.x, currentTarget.y, targetPaint);
            
            // Draw target prediction indicator
            if (config.isPredictionEnabled()) {
                drawPredictionIndicator(canvas, currentTarget);
            }
        }
        
        // Draw potential targets
        if (potentialTargets != null) {
            targetPaint.setColor(COLOR_TARGET_POTENTIAL);
            for (PointF target : potentialTargets) {
                if (target != null && !target.equals(currentTarget)) {
                    canvas.drawCircle(target.x, target.y, TARGET_CIRCLE_RADIUS/2, targetPaint);
                }
            }
        }
    }
    
    private void drawPredictionIndicator(Canvas canvas, PointF target) {
        // Draw prediction circle slightly ahead of target
        Paint predictionPaint = new Paint(targetPaint);
        predictionPaint.setAlpha(150);
        predictionPaint.setStyle(Paint.Style.FILL);
        canvas.drawCircle(target.x + 20, target.y, 10, predictionPaint);
    }
    
    private void drawTargetInfo(Canvas canvas) {
        if (currentTarget != null && targetInfo != null) {
            float textX = currentTarget.x + TARGET_CIRCLE_RADIUS + 10;
            float textY = currentTarget.y - 10;
            
            // Draw background for text
            Rect textBounds = new Rect();
            textPaint.getTextBounds(targetInfo, 0, targetInfo.length(), textBounds);
            
            Paint backgroundPaint = new Paint();
            backgroundPaint.setColor(Color.BLACK);
            backgroundPaint.setAlpha(180);
            canvas.drawRect(textX - 5, textY - textBounds.height() - 5,
                           textX + textBounds.width() + 5, textY + 5, backgroundPaint);
            
            // Draw text
            canvas.drawText(targetInfo, textX, textY, textPaint);
        }
    }
    
    private void drawStatusIndicators(Canvas canvas) {
        float y = 50;
        float x = 20;
        
        // AutoAim status
        String status = "AutoAim: " + (config.isEnabled() ? "ON" : "OFF");
        canvas.drawText(status, x, y, textPaint);
        y += 30;
        
        // Current settings
        if (config.isEnabled()) {
            canvas.drawText("Sensitivity: " + String.format("%.1f", config.getSensitivity()), x, y, textPaint);
            y += 25;
            canvas.drawText("Range: " + String.format("%.0fm", config.getRange()), x, y, textPaint);
            y += 25;
            
            if (isTargetLocked) {
                canvas.drawText("Target Locked", x, y, textPaint);
                y += 25;
                if (targetDistance > 0) {
                    canvas.drawText("Distance: " + String.format("%.1fm", targetDistance), x, y, textPaint);
                }
            }
        }
    }
    
    // Public methods for updating target information
    public void updateCurrentTarget(PointF target, boolean locked, float distance) {
        this.currentTarget = target;
        this.isTargetLocked = locked;
        this.targetDistance = distance;
        this.targetInfo = locked ? String.format("%.1fm", distance) : null;
        invalidate(); // Trigger redraw
    }
    
    public void updatePotentialTargets(PointF[] targets) {
        this.potentialTargets = targets;
        invalidate();
    }
    
    public void clearTargets() {
        this.currentTarget = null;
        this.potentialTargets = null;
        this.isTargetLocked = false;
        this.targetDistance = 0;
        this.targetInfo = null;
        invalidate();
    }
    
    public void updateConfig() {
        // Refresh configuration and redraw
        invalidate();
    }
    
    // Utility methods
    public boolean isPointInFOV(PointF point) {
        if (config.getFovLimit() <= 0) return true;
        
        float distance = (float) Math.sqrt(
            Math.pow(point.x - screenCenter.x, 2) + 
            Math.pow(point.y - screenCenter.y, 2)
        );
        
        float maxFOVRadius = (config.getFovLimit() / 180f) * Math.min(screenWidth, screenHeight) / 2;
        return distance <= maxFOVRadius;
    }
    
    public boolean isPointInRange(PointF point, float worldDistance) {
        return worldDistance <= config.getRange();
    }
}
