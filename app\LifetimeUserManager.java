package com.my.ffh4xinjector.license;

import android.content.Context;
import android.content.SharedPreferences;
import android.provider.Settings;
import android.util.Log;
import com.google.firebase.database.*;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Lifetime User Access Manager
 * Handles verification and management of lifetime user access for FFH4X
 */
public class LifetimeUserManager {
    private static final String TAG = "LifetimeUserManager";
    private static final String PREFS_NAME = "lifetime_access";
    
    // SharedPreferences Keys
    private static final String KEY_USER_ID = "user_id";
    private static final String KEY_LICENSE_KEY = "license_key";
    private static final String KEY_ACCESS_STATUS = "access_status";
    private static final String KEY_ACTIVATION_DATE = "activation_date";
    private static final String KEY_DEVICE_ID = "device_id";
    private static final String KEY_LAST_VERIFICATION = "last_verification";
    private static final String KEY_OFFLINE_GRACE_PERIOD = "offline_grace_period";
    
    // Access Status Constants
    public static final int ACCESS_DENIED = 0;
    public static final int ACCESS_TRIAL = 1;
    public static final int ACCESS_LIFETIME = 2;
    public static final int ACCESS_EXPIRED = 3;
    public static final int ACCESS_BANNED = 4;
    
    // Firebase Database Paths
    private static final String FIREBASE_USERS_PATH = "lifetime_users";
    private static final String FIREBASE_LICENSES_PATH = "license_keys";
    private static final String FIREBASE_DEVICES_PATH = "registered_devices";
    
    // Verification Settings
    private static final long VERIFICATION_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours
    private static final long OFFLINE_GRACE_PERIOD = 7 * 24 * 60 * 60 * 1000; // 7 days
    private static final int MAX_DEVICES_PER_LICENSE = 3;
    
    private Context context;
    private SharedPreferences prefs;
    private DatabaseReference firebaseDb;
    private FirebaseAuth firebaseAuth;
    private static LifetimeUserManager instance;
    
    // Listeners
    private OnAccessStatusChangeListener accessStatusListener;
    
    public interface OnAccessStatusChangeListener {
        void onAccessGranted(int accessType);
        void onAccessDenied(String reason);
        void onVerificationRequired();
        void onOfflineMode();
    }
    
    private LifetimeUserManager(Context context) {
        this.context = context.getApplicationContext();
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.firebaseDb = FirebaseDatabase.getInstance().getReference();
        this.firebaseAuth = FirebaseAuth.getInstance();

        initializeDeviceId();
        initializeCompatibilityCheck();
    }

    /**
     * Initialize compatibility check for universal device support
     */
    private void initializeCompatibilityCheck() {
        UniversalCompatibilityManager compatibilityManager =
            UniversalCompatibilityManager.getInstance(context);

        UniversalCompatibilityManager.DeviceInfo deviceInfo = compatibilityManager.getDeviceInfo();
        UniversalCompatibilityManager.CompatibilityProfile profile = compatibilityManager.getCompatibilityProfile();

        Log.d(TAG, "Device compatibility: " + profile.overallCompatibility);
        Log.d(TAG, "Device info: " + deviceInfo.toString());

        // Store compatibility info for later use
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString("device_manufacturer", deviceInfo.manufacturer);
        editor.putString("device_model", deviceInfo.model);
        editor.putString("device_category", deviceInfo.category.toString());
        editor.putString("compatibility_level", profile.overallCompatibility.toString());
        editor.putBoolean("has_root", deviceInfo.hasRoot);
        editor.putBoolean("has_magisk", deviceInfo.hasMagisk);
        editor.apply();
    }
    
    public static synchronized LifetimeUserManager getInstance(Context context) {
        if (instance == null) {
            instance = new LifetimeUserManager(context);
        }
        return instance;
    }
    
    /**
     * Initialize or retrieve device ID
     */
    private void initializeDeviceId() {
        String deviceId = prefs.getString(KEY_DEVICE_ID, null);
        if (deviceId == null) {
            deviceId = generateDeviceId();
            prefs.edit().putString(KEY_DEVICE_ID, deviceId).apply();
        }
    }
    
    /**
     * Generate unique device ID
     */
    private String generateDeviceId() {
        try {
            String androidId = Settings.Secure.getString(context.getContentResolver(), 
                Settings.Secure.ANDROID_ID);
            String deviceInfo = android.os.Build.MODEL + android.os.Build.MANUFACTURER + androidId;
            
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(deviceInfo.getBytes());
            StringBuilder hexString = new StringBuilder();
            
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            
            return hexString.toString().substring(0, 16).toUpperCase();
        } catch (Exception e) {
            Log.e(TAG, "Error generating device ID", e);
            return UUID.randomUUID().toString().replace("-", "").substring(0, 16).toUpperCase();
        }
    }
    
    /**
     * Activate lifetime access with license key
     */
    public void activateLifetimeAccess(String licenseKey, OnActivationListener listener) {
        if (licenseKey == null || licenseKey.trim().isEmpty()) {
            listener.onActivationFailed("Invalid license key");
            return;
        }
        
        String cleanLicenseKey = licenseKey.trim().toUpperCase();
        Log.d(TAG, "Attempting to activate license: " + maskLicenseKey(cleanLicenseKey));
        
        // Check license key format
        if (!isValidLicenseFormat(cleanLicenseKey)) {
            listener.onActivationFailed("Invalid license key format");
            return;
        }
        
        // Verify license key with Firebase
        verifyLicenseKey(cleanLicenseKey, new OnLicenseVerificationListener() {
            @Override
            public void onVerificationSuccess(LicenseInfo licenseInfo) {
                if (licenseInfo.isValid && !licenseInfo.isBanned) {
                    registerDevice(cleanLicenseKey, licenseInfo, listener);
                } else {
                    String reason = licenseInfo.isBanned ? "License key is banned" : "License key is invalid";
                    listener.onActivationFailed(reason);
                }
            }
            
            @Override
            public void onVerificationFailed(String error) {
                listener.onActivationFailed("Verification failed: " + error);
            }
        });
    }
    
    /**
     * Verify license key format
     */
    private boolean isValidLicenseFormat(String licenseKey) {
        // Expected format: XXXX-XXXX-XXXX-XXXX (16 characters + 3 dashes)
        return licenseKey.matches("^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$");
    }
    
    /**
     * Verify license key with Firebase
     */
    private void verifyLicenseKey(String licenseKey, OnLicenseVerificationListener listener) {
        firebaseDb.child(FIREBASE_LICENSES_PATH).child(licenseKey)
            .addListenerForSingleValueEvent(new ValueEventListener() {
                @Override
                public void onDataChange(DataSnapshot snapshot) {
                    if (snapshot.exists()) {
                        LicenseInfo licenseInfo = snapshot.getValue(LicenseInfo.class);
                        if (licenseInfo != null) {
                            listener.onVerificationSuccess(licenseInfo);
                        } else {
                            listener.onVerificationFailed("Invalid license data");
                        }
                    } else {
                        listener.onVerificationFailed("License key not found");
                    }
                }
                
                @Override
                public void onCancelled(DatabaseError error) {
                    listener.onVerificationFailed("Database error: " + error.getMessage());
                }
            });
    }
    
    /**
     * Register device for license
     */
    private void registerDevice(String licenseKey, LicenseInfo licenseInfo, OnActivationListener listener) {
        String deviceId = getDeviceId();
        
        // Check if device is already registered
        firebaseDb.child(FIREBASE_DEVICES_PATH).child(licenseKey)
            .addListenerForSingleValueEvent(new ValueEventListener() {
                @Override
                public void onDataChange(DataSnapshot snapshot) {
                    Map<String, Object> devices = new HashMap<>();
                    if (snapshot.exists()) {
                        devices = (Map<String, Object>) snapshot.getValue();
                    }
                    
                    // Check if current device is already registered
                    if (devices.containsKey(deviceId)) {
                        // Device already registered, activate access
                        saveActivationData(licenseKey, licenseInfo);
                        listener.onActivationSuccess("Welcome back! Access restored.");
                        return;
                    }
                    
                    // Check device limit
                    if (devices.size() >= MAX_DEVICES_PER_LICENSE) {
                        listener.onActivationFailed("Maximum devices limit reached for this license");
                        return;
                    }
                    
                    // Register new device
                    Map<String, Object> deviceInfo = new HashMap<>();
                    deviceInfo.put("deviceId", deviceId);
                    deviceInfo.put("registrationDate", System.currentTimeMillis());
                    deviceInfo.put("deviceModel", android.os.Build.MODEL);
                    deviceInfo.put("lastSeen", System.currentTimeMillis());
                    
                    devices.put(deviceId, deviceInfo);
                    
                    firebaseDb.child(FIREBASE_DEVICES_PATH).child(licenseKey).setValue(devices)
                        .addOnSuccessListener(aVoid -> {
                            saveActivationData(licenseKey, licenseInfo);
                            listener.onActivationSuccess("Lifetime access activated successfully!");
                        })
                        .addOnFailureListener(e -> {
                            listener.onActivationFailed("Failed to register device: " + e.getMessage());
                        });
                }
                
                @Override
                public void onCancelled(DatabaseError error) {
                    listener.onActivationFailed("Registration failed: " + error.getMessage());
                }
            });
    }
    
    /**
     * Save activation data locally
     */
    private void saveActivationData(String licenseKey, LicenseInfo licenseInfo) {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString(KEY_LICENSE_KEY, licenseKey);
        editor.putInt(KEY_ACCESS_STATUS, ACCESS_LIFETIME);
        editor.putLong(KEY_ACTIVATION_DATE, System.currentTimeMillis());
        editor.putLong(KEY_LAST_VERIFICATION, System.currentTimeMillis());
        editor.putLong(KEY_OFFLINE_GRACE_PERIOD, System.currentTimeMillis() + OFFLINE_GRACE_PERIOD);
        editor.apply();
        
        Log.d(TAG, "Lifetime access activated for license: " + maskLicenseKey(licenseKey));
        
        if (accessStatusListener != null) {
            accessStatusListener.onAccessGranted(ACCESS_LIFETIME);
        }
    }
    
    /**
     * Check current access status
     */
    public void checkAccessStatus(OnAccessCheckListener listener) {
        int currentStatus = prefs.getInt(KEY_ACCESS_STATUS, ACCESS_DENIED);
        String licenseKey = prefs.getString(KEY_LICENSE_KEY, null);
        long lastVerification = prefs.getLong(KEY_LAST_VERIFICATION, 0);
        long offlineGracePeriod = prefs.getLong(KEY_OFFLINE_GRACE_PERIOD, 0);
        
        // If no license key, deny access
        if (licenseKey == null) {
            listener.onAccessResult(ACCESS_DENIED, "No license key found");
            return;
        }
        
        // Check if verification is needed
        long timeSinceLastVerification = System.currentTimeMillis() - lastVerification;
        if (timeSinceLastVerification > VERIFICATION_INTERVAL) {
            // Try online verification
            performOnlineVerification(licenseKey, new OnVerificationResultListener() {
                @Override
                public void onVerificationSuccess() {
                    prefs.edit().putLong(KEY_LAST_VERIFICATION, System.currentTimeMillis()).apply();
                    listener.onAccessResult(ACCESS_LIFETIME, "Access verified");
                }
                
                @Override
                public void onVerificationFailed(String reason) {
                    // Check offline grace period
                    if (System.currentTimeMillis() < offlineGracePeriod) {
                        listener.onAccessResult(ACCESS_LIFETIME, "Offline mode - grace period active");
                        if (accessStatusListener != null) {
                            accessStatusListener.onOfflineMode();
                        }
                    } else {
                        listener.onAccessResult(ACCESS_EXPIRED, "Verification failed and grace period expired");
                    }
                }
            });
        } else {
            // Recent verification, grant access
            listener.onAccessResult(currentStatus, "Access granted");
        }
    }
    
    /**
     * Perform online verification
     */
    private void performOnlineVerification(String licenseKey, OnVerificationResultListener listener) {
        verifyLicenseKey(licenseKey, new OnLicenseVerificationListener() {
            @Override
            public void onVerificationSuccess(LicenseInfo licenseInfo) {
                if (licenseInfo.isValid && !licenseInfo.isBanned) {
                    // Update device last seen
                    updateDeviceLastSeen(licenseKey);
                    listener.onVerificationSuccess();
                } else {
                    String reason = licenseInfo.isBanned ? "License banned" : "License invalid";
                    listener.onVerificationFailed(reason);
                }
            }
            
            @Override
            public void onVerificationFailed(String error) {
                listener.onVerificationFailed(error);
            }
        });
    }
    
    /**
     * Update device last seen timestamp
     */
    private void updateDeviceLastSeen(String licenseKey) {
        String deviceId = getDeviceId();
        firebaseDb.child(FIREBASE_DEVICES_PATH).child(licenseKey).child(deviceId).child("lastSeen")
            .setValue(System.currentTimeMillis());
    }
    
    // Utility methods
    public String getDeviceId() {
        return prefs.getString(KEY_DEVICE_ID, "UNKNOWN");
    }
    
    public String getLicenseKey() {
        return prefs.getString(KEY_LICENSE_KEY, null);
    }
    
    public int getAccessStatus() {
        return prefs.getInt(KEY_ACCESS_STATUS, ACCESS_DENIED);
    }
    
    public boolean hasLifetimeAccess() {
        return getAccessStatus() == ACCESS_LIFETIME;
    }
    
    public long getActivationDate() {
        return prefs.getLong(KEY_ACTIVATION_DATE, 0);
    }
    
    public void setAccessStatusListener(OnAccessStatusChangeListener listener) {
        this.accessStatusListener = listener;
    }
    
    /**
     * Revoke access (for admin use)
     */
    public void revokeAccess() {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putInt(KEY_ACCESS_STATUS, ACCESS_DENIED);
        editor.remove(KEY_LICENSE_KEY);
        editor.apply();
        
        if (accessStatusListener != null) {
            accessStatusListener.onAccessDenied("Access revoked");
        }
    }
    
    /**
     * Mask license key for logging
     */
    private String maskLicenseKey(String licenseKey) {
        if (licenseKey == null || licenseKey.length() < 8) return "****";
        return licenseKey.substring(0, 4) + "-****-****-" + licenseKey.substring(licenseKey.length() - 4);
    }
    
    // Inner classes and interfaces
    public static class LicenseInfo {
        public boolean isValid;
        public boolean isBanned;
        public long creationDate;
        public String createdBy;
        public int maxDevices;
        
        public LicenseInfo() {} // Required for Firebase
    }
    
    public interface OnActivationListener {
        void onActivationSuccess(String message);
        void onActivationFailed(String reason);
    }
    
    public interface OnLicenseVerificationListener {
        void onVerificationSuccess(LicenseInfo licenseInfo);
        void onVerificationFailed(String error);
    }
    
    public interface OnAccessCheckListener {
        void onAccessResult(int accessStatus, String message);
    }
    
    public interface OnVerificationResultListener {
        void onVerificationSuccess();
        void onVerificationFailed(String reason);
    }
}
