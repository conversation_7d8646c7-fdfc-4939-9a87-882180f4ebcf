1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.ff4hxtest"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="27"
9        android:targetSdkVersion="36" />
10
11    <permission
11-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\814ffd489fe24dfa3eaf8aac60182b6b\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
12        android:name="com.example.ff4hxtest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\814ffd489fe24dfa3eaf8aac60182b6b\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\814ffd489fe24dfa3eaf8aac60182b6b\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.example.ff4hxtest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\814ffd489fe24dfa3eaf8aac60182b6b\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\814ffd489fe24dfa3eaf8aac60182b6b\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Users\<USER>\AndroidStudioProjects\FF4HXtest\app\src\main\AndroidManifest.xml:5:5-13:50
18        android:allowBackup="true"
18-->C:\Users\<USER>\AndroidStudioProjects\FF4HXtest\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\814ffd489fe24dfa3eaf8aac60182b6b\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->C:\Users\<USER>\AndroidStudioProjects\FF4HXtest\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->C:\Users\<USER>\AndroidStudioProjects\FF4HXtest\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->C:\Users\<USER>\AndroidStudioProjects\FF4HXtest\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->C:\Users\<USER>\AndroidStudioProjects\FF4HXtest\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->C:\Users\<USER>\AndroidStudioProjects\FF4HXtest\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->C:\Users\<USER>\AndroidStudioProjects\FF4HXtest\app\src\main\AndroidManifest.xml:12:9-35
28        android:theme="@style/Theme.FF4HXtest" >
28-->C:\Users\<USER>\AndroidStudioProjects\FF4HXtest\app\src\main\AndroidManifest.xml:13:9-47
29        <provider
29-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\18424d5863138f5c0eb70e5b8752a5e2\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
30            android:name="androidx.startup.InitializationProvider"
30-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\18424d5863138f5c0eb70e5b8752a5e2\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
31            android:authorities="com.example.ff4hxtest.androidx-startup"
31-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\18424d5863138f5c0eb70e5b8752a5e2\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
32            android:exported="false" >
32-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\18424d5863138f5c0eb70e5b8752a5e2\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
33            <meta-data
33-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\18424d5863138f5c0eb70e5b8752a5e2\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
34                android:name="androidx.emoji2.text.EmojiCompatInitializer"
34-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\18424d5863138f5c0eb70e5b8752a5e2\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
35                android:value="androidx.startup" />
35-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\18424d5863138f5c0eb70e5b8752a5e2\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
36            <meta-data
36-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa266e446481720bbcded27d3fac07d5\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
37                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
37-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa266e446481720bbcded27d3fac07d5\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
38                android:value="androidx.startup" />
38-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa266e446481720bbcded27d3fac07d5\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
39            <meta-data
39-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
40                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
40-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
41                android:value="androidx.startup" />
41-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
42        </provider>
43
44        <receiver
44-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
45            android:name="androidx.profileinstaller.ProfileInstallReceiver"
45-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
46            android:directBootAware="false"
46-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
47            android:enabled="true"
47-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
48            android:exported="true"
48-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
49            android:permission="android.permission.DUMP" >
49-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
50            <intent-filter>
50-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
51                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
51-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
51-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
52            </intent-filter>
53            <intent-filter>
53-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
54                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
54-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
54-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
55            </intent-filter>
56            <intent-filter>
56-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
57                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
57-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
57-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
58            </intent-filter>
59            <intent-filter>
59-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
60                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
60-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
60-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
61            </intent-filter>
62        </receiver>
63    </application>
64
65</manifest>
