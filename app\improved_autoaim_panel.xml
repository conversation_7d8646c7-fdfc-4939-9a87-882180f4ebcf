<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/panel_background"
    android:elevation="8dp">

    <!-- Header Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingBottom="12dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_crosshair"
            android:tint="@color/accent_color" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="AutoAim Settings"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginStart="8dp" />

        <Switch
            android:id="@+id/switch_autoaim_master"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Enable"
            android:textColor="@color/text_secondary" />

    </LinearLayout>

    <!-- Main Settings Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/card_background">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Aim Sensitivity -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Aim Sensitivity"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="8dp" />

            <SeekBar
                android:id="@+id/seekbar_sensitivity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:max="100"
                android:progress="50"
                android:progressTint="@color/accent_color"
                android:thumbTint="@color/accent_color" />

            <TextView
                android:id="@+id/text_sensitivity_value"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="50%"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:gravity="center"
                android:layout_marginBottom="16dp" />

            <!-- Aim Range -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Aim Range (meters)"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="8dp" />

            <SeekBar
                android:id="@+id/seekbar_range"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:max="500"
                android:progress="100"
                android:progressTint="@color/accent_color"
                android:thumbTint="@color/accent_color" />

            <TextView
                android:id="@+id/text_range_value"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="100m"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:gravity="center"
                android:layout_marginBottom="16dp" />

            <!-- Target Priority -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Target Priority"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="8dp" />

            <Spinner
                android:id="@+id/spinner_target_priority"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:background="@drawable/spinner_background"
                android:layout_marginBottom="16dp" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Advanced Settings Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/card_background">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Advanced Settings"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="12dp" />

            <!-- Aim Smoothing -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Aim Smoothing"
                    android:textSize="14sp"
                    android:textColor="@color/text_primary" />

                <Switch
                    android:id="@+id/switch_smoothing"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

            </LinearLayout>

            <!-- Head Priority -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Headshot Priority"
                    android:textSize="14sp"
                    android:textColor="@color/text_primary" />

                <Switch
                    android:id="@+id/switch_headshot"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

            </LinearLayout>

            <!-- Visible Targets Only -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Visible Targets Only"
                    android:textSize="14sp"
                    android:textColor="@color/text_primary" />

                <Switch
                    android:id="@+id/switch_visible_only"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

            </LinearLayout>

            <!-- Auto Fire -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Auto Fire"
                    android:textSize="14sp"
                    android:textColor="@color/text_primary" />

                <Switch
                    android:id="@+id/switch_auto_fire"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <Button
            android:id="@+id/btn_reset"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:text="Reset"
            android:textColor="@color/text_secondary"
            android:background="@drawable/button_secondary"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_apply"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:text="Apply"
            android:textColor="@color/white"
            android:background="@drawable/button_primary"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>
