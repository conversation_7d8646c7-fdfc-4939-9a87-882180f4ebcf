// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext.kotlin_version = '1.9.20'
    
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
        maven { url 'https://jitpack.io' }
        maven { url 'https://maven.google.com' }
    }
    
    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.0'
        classpath 'com.google.gms:google-services:4.4.0'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
        maven { url 'https://maven.google.com' }
        maven { url 'https://oss.sonatype.org/content/repositories/snapshots/' }
        
        // Shizuku repository
        maven { 
            url 'https://api.xposed.info/' 
            content {
                includeGroup 'de.robv.android.xposed'
            }
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

// Global configuration
ext {
    compileSdkVersion = 34
    buildToolsVersion = "34.0.0"
    minSdkVersion = 21
    targetSdkVersion = 34
    
    // Dependency versions
    androidxVersion = '1.6.1'
    materialVersion = '1.11.0'
    firebaseVersion = '32.7.0'
    shizukuVersion = '13.1.5'
    retrofitVersion = '2.9.0'
    okhttpVersion = '4.12.0'
    glideVersion = '4.16.0'
    
    // Test versions
    junitVersion = '4.13.2'
    espressoVersion = '3.5.1'
    mockitoVersion = '5.7.0'
}
