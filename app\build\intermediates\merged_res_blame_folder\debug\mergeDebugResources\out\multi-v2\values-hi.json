{"logs": [{"outputFile": "com.example.ff4hxtest.app-mergeDebugResources-29:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ee3d26571961f0aa7200ffb9063cc008\\transformed\\material-1.10.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,413,494,601,697,804,936,1019,1084,1178,1247,1306,1391,1454,1517,1575,1640,1701,1762,1868,1926,1986,2045,2115,2231,2310,2390,2524,2599,2675,2812,2909,3007,3064,3119,3185,3255,3332,3418,3503,3571,3647,3728,3806,3907,3993,4080,4177,4276,4350,4420,4524,4578,4665,4732,4822,4914,4976,5040,5103,5169,5274,5384,5485,5592,5653,5712", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,75,80,106,95,106,131,82,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,79,133,74,75,136,96,97,56,54,65,69,76,85,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78", "endOffsets": "254,332,408,489,596,692,799,931,1014,1079,1173,1242,1301,1386,1449,1512,1570,1635,1696,1757,1863,1921,1981,2040,2110,2226,2305,2385,2519,2594,2670,2807,2904,3002,3059,3114,3180,3250,3327,3413,3498,3566,3642,3723,3801,3902,3988,4075,4172,4271,4345,4415,4519,4573,4660,4727,4817,4909,4971,5035,5098,5164,5269,5379,5480,5587,5648,5707,5786"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2991,3069,3145,3226,3333,4182,4289,4421,4504,4569,4663,4732,4791,4876,4939,5002,5060,5125,5186,5247,5353,5411,5471,5530,5600,5716,5795,5875,6009,6084,6160,6297,6394,6492,6549,6604,6670,6740,6817,6903,6988,7056,7132,7213,7291,7392,7478,7565,7662,7761,7835,7905,8009,8063,8150,8217,8307,8399,8461,8525,8588,8654,8759,8869,8970,9077,9138,9197", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "12,77,75,80,106,95,106,131,82,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,79,133,74,75,136,96,97,56,54,65,69,76,85,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78", "endOffsets": "304,3064,3140,3221,3328,3424,4284,4416,4499,4564,4658,4727,4786,4871,4934,4997,5055,5120,5181,5242,5348,5406,5466,5525,5595,5711,5790,5870,6004,6079,6155,6292,6389,6487,6544,6599,6665,6735,6812,6898,6983,7051,7127,7208,7286,7387,7473,7560,7657,7756,7830,7900,8004,8058,8145,8212,8302,8394,8456,8520,8583,8649,8754,8864,8965,9072,9133,9192,9271"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dda8a98543493039d310e3429c5e4dd4\\transformed\\appcompat-1.6.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,415,513,623,709,811,932,1010,1087,1178,1271,1366,1460,1560,1653,1748,1842,1933,2024,2105,2210,2312,2410,2520,2623,2732,2890,9276", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "410,508,618,704,806,927,1005,1082,1173,1266,1361,1455,1555,1648,1743,1837,1928,2019,2100,2205,2307,2405,2515,2618,2727,2885,2986,9353"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\814ffd489fe24dfa3eaf8aac60182b6b\\transformed\\core-1.10.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3429,3527,3630,3735,3836,3949,4055,9358", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "3522,3625,3730,3831,3944,4050,4177,9454"}}]}]}