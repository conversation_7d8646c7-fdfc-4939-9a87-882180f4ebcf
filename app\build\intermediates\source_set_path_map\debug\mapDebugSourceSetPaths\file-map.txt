com.example.ff4hxtest.app-drawerlayout-1.1.1-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\0e8da5dfb1e77968cc36751edb3ccd4a\transformed\drawerlayout-1.1.1\res
com.example.ff4hxtest.app-activity-1.8.0-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\1648fbf7355b24cfa9b009c410b30cf7\transformed\activity-1.8.0\res
com.example.ff4hxtest.app-emoji2-1.2.0-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\18424d5863138f5c0eb70e5b8752a5e2\transformed\emoji2-1.2.0\res
com.example.ff4hxtest.app-transition-1.2.0-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\1fb717b15865d8a04671a32085bb7f12\transformed\transition-1.2.0\res
com.example.ff4hxtest.app-lifecycle-livedata-2.6.1-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\257a1ac1a859eb602896cbc3225018f1\transformed\lifecycle-livedata-2.6.1\res
com.example.ff4hxtest.app-emoji2-views-helper-1.2.0-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\2913478dc567c3d695faf27c280c2305\transformed\emoji2-views-helper-1.2.0\res
com.example.ff4hxtest.app-coordinatorlayout-1.1.0-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\2fd589b355a7541769e1ffb155cd24fa\transformed\coordinatorlayout-1.1.0\res
com.example.ff4hxtest.app-annotation-experimental-1.3.0-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\3bbf0c7f58f3d19617dfa7b63eb0a1e9\transformed\annotation-experimental-1.3.0\res
com.example.ff4hxtest.app-core-ktx-1.10.1-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\4cfdfd0764ae6fbf98760ea59b791cfd\transformed\core-ktx-1.10.1\res
com.example.ff4hxtest.app-lifecycle-livedata-core-2.6.1-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\5a4f096d190449b268ce8946b7b3b837\transformed\lifecycle-livedata-core-2.6.1\res
com.example.ff4hxtest.app-cardview-1.0.0-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\63109d383aa167b34143109f9d363633\transformed\cardview-1.0.0\res
com.example.ff4hxtest.app-savedstate-1.2.1-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\67b34df9f8efb2af0598e3f78de101d9\transformed\savedstate-1.2.1\res
com.example.ff4hxtest.app-core-runtime-2.2.0-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\6b1819fce2ad51890b0d019625539d1c\transformed\core-runtime-2.2.0\res
com.example.ff4hxtest.app-fragment-1.3.6-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\6f558da8cf3c017f18b64c7ef840e2c0\transformed\fragment-1.3.6\res
com.example.ff4hxtest.app-recyclerview-1.1.0-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\7be5809fddccdc635215bec5d75e9cd8\transformed\recyclerview-1.1.0\res
com.example.ff4hxtest.app-core-1.10.1-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\814ffd489fe24dfa3eaf8aac60182b6b\transformed\core-1.10.1\res
com.example.ff4hxtest.app-profileinstaller-1.3.0-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\87d8fe551897ad1f5137dad47dcbbf53\transformed\profileinstaller-1.3.0\res
com.example.ff4hxtest.app-lifecycle-runtime-2.6.1-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\8d866b1ef78263e21af30c4804288fcf\transformed\lifecycle-runtime-2.6.1\res
com.example.ff4hxtest.app-constraintlayout-2.0.1-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\8f056e0b6f5054fd3330a5bdbcbfadfc\transformed\constraintlayout-2.0.1\res
com.example.ff4hxtest.app-lifecycle-process-2.6.1-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\aa266e446481720bbcded27d3fac07d5\transformed\lifecycle-process-2.6.1\res
com.example.ff4hxtest.app-viewpager2-1.0.0-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\b21844e157ea7b8fea58d5cc5a95e9ed\transformed\viewpager2-1.0.0\res
com.example.ff4hxtest.app-lifecycle-viewmodel-savedstate-2.6.1-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\b7f4bd0ed70e207350df71a402122de8\transformed\lifecycle-viewmodel-savedstate-2.6.1\res
com.example.ff4hxtest.app-lifecycle-viewmodel-2.6.1-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\c5cf79b211bc30b566b2932d31664e79\transformed\lifecycle-viewmodel-2.6.1\res
com.example.ff4hxtest.app-appcompat-resources-1.6.1-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\cd98c8a511d7bb89c9380b2cb87aef7b\transformed\appcompat-resources-1.6.1\res
com.example.ff4hxtest.app-appcompat-1.6.1-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\dda8a98543493039d310e3429c5e4dd4\transformed\appcompat-1.6.1\res
com.example.ff4hxtest.app-material-1.10.0-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\ee3d26571961f0aa7200ffb9063cc008\transformed\material-1.10.0\res
com.example.ff4hxtest.app-startup-runtime-1.1.1-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\f3e99e1f69d75f40f6a7a7f31aba07cc\transformed\startup-runtime-1.1.1\res
com.example.ff4hxtest.app-pngs-27 C:\Users\<USER>\AndroidStudioProjects\FF4HXtest\app\build\generated\res\pngs\debug
com.example.ff4hxtest.app-resValues-28 C:\Users\<USER>\AndroidStudioProjects\FF4HXtest\app\build\generated\res\resValues\debug
com.example.ff4hxtest.app-packageDebugResources-29 C:\Users\<USER>\AndroidStudioProjects\FF4HXtest\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.ff4hxtest.app-packageDebugResources-30 C:\Users\<USER>\AndroidStudioProjects\FF4HXtest\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.ff4hxtest.app-debug-31 C:\Users\<USER>\AndroidStudioProjects\FF4HXtest\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.ff4hxtest.app-debug-32 C:\Users\<USER>\AndroidStudioProjects\FF4HXtest\app\src\debug\res
com.example.ff4hxtest.app-main-33 C:\Users\<USER>\AndroidStudioProjects\FF4HXtest\app\src\main\res
