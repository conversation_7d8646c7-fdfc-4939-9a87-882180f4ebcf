{"logs": [{"outputFile": "com.example.ff4hxtest.app-mergeDebugResources-29:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\814ffd489fe24dfa3eaf8aac60182b6b\\transformed\\core-1.10.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "39,40,41,42,43,44,45,110", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3704,3802,3904,4004,4105,4212,4320,9664", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "3797,3899,3999,4100,4207,4315,4430,9760"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ee3d26571961f0aa7200ffb9063cc008\\transformed\\material-1.10.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,411,496,577,682,770,871,1005,1088,1153,1247,1320,1381,1506,1572,1640,1701,1773,1833,1887,2007,2067,2129,2183,2260,2390,2477,2559,2700,2780,2865,2992,3083,3159,3213,3266,3332,3406,3487,3571,3651,3724,3801,3878,3952,4062,4155,4230,4320,4411,4483,4561,4652,4706,4789,4857,4941,5028,5090,5154,5217,5289,5399,5512,5615,5724,5782,5839", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,86,84,80,104,87,100,133,82,64,93,72,60,124,65,67,60,71,59,53,119,59,61,53,76,129,86,81,140,79,84,126,90,75,53,52,65,73,80,83,79,72,76,76,73,109,92,74,89,90,71,77,90,53,82,67,83,86,61,63,62,71,109,112,102,108,57,56,76", "endOffsets": "319,406,491,572,677,765,866,1000,1083,1148,1242,1315,1376,1501,1567,1635,1696,1768,1828,1882,2002,2062,2124,2178,2255,2385,2472,2554,2695,2775,2860,2987,3078,3154,3208,3261,3327,3401,3482,3566,3646,3719,3796,3873,3947,4057,4150,4225,4315,4406,4478,4556,4647,4701,4784,4852,4936,5023,5085,5149,5212,5284,5394,5507,5610,5719,5777,5834,5911"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3258,3345,3430,3511,3616,4435,4536,4670,4753,4818,4912,4985,5046,5171,5237,5305,5366,5438,5498,5552,5672,5732,5794,5848,5925,6055,6142,6224,6365,6445,6530,6657,6748,6824,6878,6931,6997,7071,7152,7236,7316,7389,7466,7543,7617,7727,7820,7895,7985,8076,8148,8226,8317,8371,8454,8522,8606,8693,8755,8819,8882,8954,9064,9177,9280,9389,9447,9504", "endLines": "6,34,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108", "endColumns": "12,86,84,80,104,87,100,133,82,64,93,72,60,124,65,67,60,71,59,53,119,59,61,53,76,129,86,81,140,79,84,126,90,75,53,52,65,73,80,83,79,72,76,76,73,109,92,74,89,90,71,77,90,53,82,67,83,86,61,63,62,71,109,112,102,108,57,56,76", "endOffsets": "369,3340,3425,3506,3611,3699,4531,4665,4748,4813,4907,4980,5041,5166,5232,5300,5361,5433,5493,5547,5667,5727,5789,5843,5920,6050,6137,6219,6360,6440,6525,6652,6743,6819,6873,6926,6992,7066,7147,7231,7311,7384,7461,7538,7612,7722,7815,7890,7980,8071,8143,8221,8312,8366,8449,8517,8601,8688,8750,8814,8877,8949,9059,9172,9275,9384,9442,9499,9576"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dda8a98543493039d310e3429c5e4dd4\\transformed\\appcompat-1.6.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,3067"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "374,494,604,713,799,903,1025,1107,1187,1297,1405,1511,1620,1731,1834,1946,2053,2158,2258,2343,2452,2563,2662,2773,2880,2985,3159,9581", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "489,599,708,794,898,1020,1102,1182,1292,1400,1506,1615,1726,1829,1941,2048,2153,2253,2338,2447,2558,2657,2768,2875,2980,3154,3253,9659"}}]}]}